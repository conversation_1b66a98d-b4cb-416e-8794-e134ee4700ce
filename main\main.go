// Example Code generated by Beijing Volcanoengine Technology.
package main

import (
	"github.com/volcengine/volcengine-go-sdk/service/ecs"
	"github.com/volcengine/volcengine-go-sdk/volcengine"
	"github.com/volcengine/volcengine-go-sdk/volcengine/credentials"
	"github.com/volcengine/volcengine-go-sdk/volcengine/session"
)

func main() {
	// 注意示例代码安全，代码泄漏会导致AK/SK泄漏，有极大的安全风险。
	ak, sk, region := "Your AK", "Your SK", "cn-beijing"
	config := volcengine.NewConfig().
		WithRegion(region).
		WithCredentials(credentials.NewStaticCredentials(ak, sk, ""))
	sess, err := session.NewSession(config)
	if err != nil {
		panic(err)
	}
	svc := ecs.New(sess)
	describeInstancesInput := &ecs.DescribeInstancesInput{}

	// 复制代码运行示例，请自行打印API返回值。
	_, err = svc.DescribeInstances(describeInstancesInput)
	if err != nil {
		// 复制代码运行示例，请自行打印API错误信息。
		panic(err)
	}
}
