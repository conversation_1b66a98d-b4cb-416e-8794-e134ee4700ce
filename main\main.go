// Example Code generated by Beijing Volcanoengine Technology.
package main

import (
	"encoding/json"
	"fmt"
	"log"
	"os"

	"github.com/volcengine/volcengine-go-sdk/service/ecs"
	"github.com/volcengine/volcengine-go-sdk/volcengine"
	"github.com/volcengine/volcengine-go-sdk/volcengine/credentials"
	"github.com/volcengine/volcengine-go-sdk/volcengine/session"
)

func main() {
	// 从环境变量获取凭证，更安全的做法
	ak := os.Getenv("VOLC_ACCESSKEY")
	sk := os.Getenv("VOLC_SECRETKEY")
	region := os.Getenv("VOLC_REGION")

	// 如果环境变量为空，使用默认值（仅用于测试）
	if ak == "" {
		ak = "Your AK"
	}
	if sk == "" {
		sk = "Your SK"
	}
	if region == "" {
		region = "cn-beijing"
	}

	config := volcengine.NewConfig().
		WithRegion(region).
		WithCredentials(credentials.NewStaticCredentials(ak, sk, ""))

	sess, err := session.NewSession(config)
	if err != nil {
		log.Fatalf("创建会话失败: %v", err)
	}

	svc := ecs.New(sess)

	// 获取 ECS 实例和磁盘使用量信息
	err = getInstancesAndDiskUsage(svc)
	if err != nil {
		log.Fatalf("获取实例和磁盘信息失败: %v", err)
	}
}

// getInstancesAndDiskUsage 获取实例和磁盘使用量信息
func getInstancesAndDiskUsage(svc *ecs.ECS) error {
	// 1. 获取实例列表
	fmt.Println("=== 获取 ECS 实例列表 ===")
	describeInstancesInput := &ecs.DescribeInstancesInput{}

	instancesResp, err := svc.DescribeInstances(describeInstancesInput)
	if err != nil {
		return fmt.Errorf("调用 DescribeInstances 失败: %v", err)
	}

	fmt.Printf("找到 %d 个实例\n\n", len(instancesResp.Instances))

	// 收集所有磁盘 ID
	var volumeIds []*string
	instanceVolumeMap := make(map[string][]string) // 实例ID -> 磁盘ID列表

	for _, instance := range instancesResp.Instances {
		fmt.Printf("实例 ID: %s\n", *instance.InstanceId)
		fmt.Printf("实例名称: %s\n", *instance.InstanceName)
		fmt.Printf("实例状态: %s\n", *instance.Status)

		var instanceVolumeIds []string

		// 系统盘
		if instance.SystemVolumeId != nil {
			volumeIds = append(volumeIds, instance.SystemVolumeId)
			instanceVolumeIds = append(instanceVolumeIds, *instance.SystemVolumeId)
			fmt.Printf("系统盘 ID: %s\n", *instance.SystemVolumeId)
		}

		// 数据盘
		for _, volumeId := range instance.DataVolumeIds {
			volumeIds = append(volumeIds, volumeId)
			instanceVolumeIds = append(instanceVolumeIds, *volumeId)
			fmt.Printf("数据盘 ID: %s\n", *volumeId)
		}

		instanceVolumeMap[*instance.InstanceId] = instanceVolumeIds
		fmt.Println("---")
	}

	// 2. 获取磁盘详细信息
	if len(volumeIds) > 0 {
		fmt.Println("\n=== 获取磁盘详细信息 ===")
		err = getVolumeDetails(svc, volumeIds, instanceVolumeMap)
		if err != nil {
			return fmt.Errorf("获取磁盘详细信息失败: %v", err)
		}
	}

	return nil
}

// getVolumeDetails 获取磁盘详细信息
func getVolumeDetails(svc *ecs.ECS, volumeIds []*string, instanceVolumeMap map[string][]string) error {
	describeVolumesInput := &ecs.DescribeVolumesInput{
		VolumeIds: volumeIds,
	}

	volumesResp, err := svc.DescribeVolumes(describeVolumesInput)
	if err != nil {
		return fmt.Errorf("调用 DescribeVolumes 失败: %v", err)
	}

	fmt.Printf("找到 %d 个磁盘\n\n", len(volumesResp.Volumes))

	// 创建磁盘ID到磁盘信息的映射
	volumeMap := make(map[string]*ecs.VolumeForDescribeVolumesOutput)
	for _, volume := range volumesResp.Volumes {
		volumeMap[*volume.VolumeId] = volume
	}

	// 按实例分组显示磁盘信息
	for instanceId, volumeIdList := range instanceVolumeMap {
		fmt.Printf("实例 %s 的磁盘信息:\n", instanceId)

		var totalSize int64 = 0

		for _, volumeId := range volumeIdList {
			if volume, exists := volumeMap[volumeId]; exists {
				fmt.Printf("  磁盘 ID: %s\n", *volume.VolumeId)
				fmt.Printf("  磁盘名称: %s\n", *volume.VolumeName)
				fmt.Printf("  磁盘类型: %s\n", *volume.VolumeType)
				fmt.Printf("  磁盘大小: %d GB\n", *volume.Size)
				fmt.Printf("  磁盘状态: %s\n", *volume.Status)

				if volume.Kind != nil {
					fmt.Printf("  磁盘种类: %s\n", *volume.Kind)
				}

				totalSize += *volume.Size
				fmt.Println("  ---")
			}
		}

		fmt.Printf("  总磁盘容量: %d GB\n", totalSize)
		fmt.Println("========================================")
	}

	// 输出 JSON 格式的详细信息（可选）
	fmt.Println("\n=== JSON 格式的磁盘详细信息 ===")
	for _, volume := range volumesResp.Volumes {
		volumeJSON, err := json.MarshalIndent(volume, "", "  ")
		if err != nil {
			log.Printf("序列化磁盘信息失败: %v", err)
			continue
		}
		fmt.Printf("磁盘 %s 详细信息:\n%s\n\n", *volume.VolumeId, string(volumeJSON))
	}

	return nil
}
