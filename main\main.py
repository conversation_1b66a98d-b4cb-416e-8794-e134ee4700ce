# Example Code generated by Beijing Volcanoengine Technology.
from __future__ import print_function
import volcenginesdkcore
import volcenginesdkvpc
from volcenginesdkcore.rest import ApiException
import json

def safe_get_attr(obj, attr_name, default=None):
    """安全获取对象属性"""
    try:
        return getattr(obj, attr_name, default)
    except Exception:
        return default

def safe_iterate(obj):
    """安全迭代对象，如果不可迭代则返回单元素列表"""
    if obj is None:
        return []

    # 如果是字符串，不要迭代
    if isinstance(obj, str):
        return [obj]

    # 尝试迭代
    try:
        # 检查是否有 __iter__ 方法
        if hasattr(obj, '__iter__'):
            return list(obj)
        else:
            return [obj]
    except Exception:
        return [obj]

def print_network_interface_info(network_interfaces):
    """打印网络接口详细信息并提取绑定对象ID"""
    print("=" * 80)
    print("网络接口详细信息:")
    print("=" * 80)

    for i, interface in enumerate(network_interfaces, 1):
        print(f"\n--- 网络接口 #{i} ---")
        print(f"网络接口ID: {safe_get_attr(interface, 'network_interface_id', 'N/A')}")
        print(f"网络接口名称: {safe_get_attr(interface, 'network_interface_name', 'N/A')}")
        print(f"状态: {safe_get_attr(interface, 'status', 'N/A')}")
        print(f"类型: {safe_get_attr(interface, 'type', 'N/A')}")
        print(f"MAC地址: {safe_get_attr(interface, 'mac_address', 'N/A')}")
        print(f"主IP地址: {safe_get_attr(interface, 'primary_ip_address', 'N/A')}")
        print(f"VPC ID: {safe_get_attr(interface, 'vpc_id', 'N/A')}")
        print(f"子网ID: {safe_get_attr(interface, 'subnet_id', 'N/A')}")
        print(f"可用区: {safe_get_attr(interface, 'zone_id', 'N/A')}")
        print(f"创建时间: {safe_get_attr(interface, 'created_at', 'N/A')}")
        print(f"更新时间: {safe_get_attr(interface, 'updated_at', 'N/A')}")

        # 提取网卡绑定对象的ID
        print(f"\n🔗 绑定对象信息:")
        instance_id = safe_get_attr(interface, 'instance_id')
        if instance_id:
            print(f"  绑定的实例ID: {instance_id}")

        device_id = safe_get_attr(interface, 'device_id')
        if device_id:
            print(f"  设备ID: {device_id}")

        device_owner = safe_get_attr(interface, 'device_owner')
        if device_owner:
            print(f"  设备所有者: {device_owner}")

        # 安全组信息
        security_group_ids = safe_get_attr(interface, 'security_group_ids')
        if security_group_ids:
            sg_list = safe_iterate(security_group_ids)
            print(f"  安全组IDs: {', '.join(str(sg) for sg in sg_list)}")

        # 私有IP地址
        private_ip_sets = safe_get_attr(interface, 'private_ip_sets')
        if private_ip_sets:
            print(f"  私有IP地址:")
            ip_sets_list = safe_iterate(private_ip_sets)
            for ip_set in ip_sets_list:
                ip_address = safe_get_attr(ip_set, 'private_ip_address', 'N/A')
                is_primary = safe_get_attr(ip_set, 'primary', False)
                association_id = safe_get_attr(ip_set, 'association_id')

                print(f"    - IP: {ip_address}, 主IP: {is_primary}")
                if association_id:
                    print(f"      关联ID: {association_id}")

        # 标签信息
        tags = safe_get_attr(interface, 'tags')
        if tags:
            print(f"  标签:")
            tags_list = safe_iterate(tags)
            for tag in tags_list:
                key = safe_get_attr(tag, 'key', 'N/A')
                value = safe_get_attr(tag, 'value', 'N/A')
                print(f"    - {key}: {value}")

        print("-" * 60)

def extract_binding_ids(network_interfaces):
    """提取所有网卡绑定对象的ID"""
    binding_info = []

    for interface in network_interfaces:
        binding_data = {
            'network_interface_id': safe_get_attr(interface, 'network_interface_id', 'N/A'),
            'network_interface_name': safe_get_attr(interface, 'network_interface_name', 'N/A'),
            'binding_objects': []
        }

        # 提取各种绑定对象ID
        instance_id = safe_get_attr(interface, 'instance_id')
        if instance_id:
            binding_data['binding_objects'].append({
                'type': 'ECS实例',
                'id': instance_id
            })

        device_id = safe_get_attr(interface, 'device_id')
        if device_id:
            binding_data['binding_objects'].append({
                'type': '设备',
                'id': device_id
            })

        # 如果有私有IP关联
        private_ip_sets = safe_get_attr(interface, 'private_ip_sets')
        if private_ip_sets:
            ip_sets_list = safe_iterate(private_ip_sets)
            for ip_set in ip_sets_list:
                association_id = safe_get_attr(ip_set, 'association_id')
                if association_id:
                    ip_address = safe_get_attr(ip_set, 'private_ip_address', 'N/A')
                    binding_data['binding_objects'].append({
                        'type': 'IP关联',
                        'id': association_id,
                        'ip': ip_address
                    })

        binding_info.append(binding_data)

    return binding_info

if __name__ == '__main__':
    # 注意示例代码安全，代码泄漏会导致AK/SK泄漏，有极大的安全风险。
    configuration = volcenginesdkcore.Configuration()
    configuration.ak = "AKLTMGVmYWFiOTRlYzJhNDJkNmFkNmY2YmVmZjc5MTNiNjI"
    configuration.sk = "T0RaaE9UY3hZVEkwTnpNM05EQXpPV0k0TW1OaE9UWm1PV0ZoTWpkak9Eaw=="
    configuration.region = "cn-shanghai"
    # set default configuration
    volcenginesdkcore.Configuration.set_default(configuration)

    # use global default configuration
    api_instance = volcenginesdkvpc.VPCApi()
    describe_network_interfaces_request = volcenginesdkvpc.DescribeNetworkInterfacesRequest()

    try:
        print("🚀 开始查询网络接口信息...")

        # 调用API并获取返回值
        response = api_instance.describe_network_interfaces(describe_network_interfaces_request)

        print(f"✅ API调用成功!")
        print(f"📊 返回的网络接口数量: {len(response.network_interface_sets)}")

        # 打印完整的API返回值（JSON格式）
        print("\n" + "=" * 80)
        print("完整API返回值 (JSON格式):")
        print("=" * 80)

        # 将响应对象转换为字典并打印
        response_dict = response.to_dict()
        print(json.dumps(response_dict, indent=2, ensure_ascii=False, default=str))

        # 打印详细的网络接口信息
        if response.network_interface_sets:
            print_network_interface_info(response.network_interface_sets)

            # 提取并打印绑定对象ID
            print("\n" + "=" * 80)
            print("🔗 网卡绑定对象ID汇总:")
            print("=" * 80)

            binding_info = extract_binding_ids(response.network_interface_sets)

            for info in binding_info:
                print(f"\n网卡: {info['network_interface_name']} ({info['network_interface_id']})")
                if info['binding_objects']:
                    for binding in info['binding_objects']:
                        if 'ip' in binding:
                            print(f"  - {binding['type']}: {binding['id']} (IP: {binding['ip']})")
                        else:
                            print(f"  - {binding['type']}: {binding['id']}")
                else:
                    print("  - 无绑定对象")

            # 输出绑定对象ID列表（便于后续使用）
            print("\n" + "=" * 80)
            print("📋 所有绑定对象ID列表:")
            print("=" * 80)

            all_binding_ids = []
            for info in binding_info:
                for binding in info['binding_objects']:
                    all_binding_ids.append(binding['id'])

            if all_binding_ids:
                print("绑定对象ID列表:")
                for binding_id in set(all_binding_ids):  # 去重
                    print(f"  - {binding_id}")

                print(f"\n总计: {len(set(all_binding_ids))} 个唯一的绑定对象ID")
            else:
                print("未找到任何绑定对象ID")
        else:
            print("⚠️  未找到任何网络接口")

    except ApiException as e:
        print(f"❌ API调用失败:")
        print(f"错误代码: {e.status}")
        print(f"错误信息: {e.reason}")
        print(f"错误详情: {e.body}")
    except Exception as e:
        print(f"❌ 程序执行出错: {str(e)}")
        import traceback
        traceback.print_exc()
