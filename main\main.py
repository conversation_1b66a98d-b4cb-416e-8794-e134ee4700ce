# Example Code generated by Beijing Volcanoengine Technology.
from __future__ import print_function
import volcenginesdkcore
import volcenginesdkvpc
from volcenginesdkcore.rest import ApiException
import json

def safe_get_attr(obj, attr_name, default=None):
    """安全获取对象属性"""
    try:
        return getattr(obj, attr_name, default)
    except Exception:
        return default

def safe_iterate(obj):
    """安全迭代对象，如果不可迭代则返回单元素列表"""
    if obj is None:
        return []

    # 如果是字符串，不要迭代
    if isinstance(obj, str):
        return [obj]

    # 尝试迭代
    try:
        # 检查是否有 __iter__ 方法
        if hasattr(obj, '__iter__'):
            return list(obj)
        else:
            return [obj]
    except Exception:
        return [obj]

# 移除了详细打印函数，保持代码简洁

# 移除了复杂的分析函数，保持代码简洁专注

def query_region_interfaces(region_name, ak, sk):
    """查询指定区域的网络接口"""
    print(f"\n🌍 正在查询区域: {region_name}")

    configuration = volcenginesdkcore.Configuration()
    configuration.ak = ak
    configuration.sk = sk
    configuration.region = region_name
    volcenginesdkcore.Configuration.set_default(configuration)

    api_instance = volcenginesdkvpc.VPCApi()

    try:
        all_interfaces = []
        page_number = 1
        page_size = 100

        while True:
            describe_network_interfaces_request = volcenginesdkvpc.DescribeNetworkInterfacesRequest(
                page_number=page_number,
                page_size=page_size
            )

            response = api_instance.describe_network_interfaces(describe_network_interfaces_request)

            if response.network_interface_sets:
                all_interfaces.extend(response.network_interface_sets)
                if len(response.network_interface_sets) < page_size:
                    break
                page_number += 1
            else:
                break

        print(f"   ✅ {region_name} 区域找到 {len(all_interfaces)} 张网卡")
        return all_interfaces

    except Exception as e:
        print(f"   ❌ {region_name} 区域查询失败: {str(e)}")
        return []

if __name__ == '__main__':
    # 注意示例代码安全，代码泄漏会导致AK/SK泄漏，有极大的安全风险。
    ak = "AKLTMGVmYWFiOTRlYzJhNDJkNmFkNmY2YmVmZjc5MTNiNjI"
    sk = "T0RaaE9UY3hZVEkwTnpNM05EQXpPV0k0TW1OaE9UWm1PV0ZoTWpkak9Eaw=="

    # 常用的火山引擎区域列表
    regions = [
        "cn-shanghai",    # 华东2（上海）
        "cn-beijing",     # 华北2（北京）
        "cn-guangzhou",   # 华南1（广州）
        "cn-shenzhen",    # 华南2（深圳）
        "ap-singapore",   # 亚太（新加坡）
        "us-east-1"       # 美国东部
    ]

    print("🚀 开始多区域网络接口查询...")
    print(f"📋 将查询以下区域: {', '.join(regions)}")

    all_regions_interfaces = []
    region_stats = {}

    for region in regions:
        interfaces = query_region_interfaces(region, ak, sk)
        all_regions_interfaces.extend(interfaces)
        region_stats[region] = len(interfaces)

    print(f"\n🎉 多区域查询完成！")
    print(f"📊 各区域网卡统计:")
    total_all_regions = 0
    for region, count in region_stats.items():
        print(f"   {region}: {count} 张网卡")
        total_all_regions += count

    print(f"\n🌐 全部区域网卡总数: {total_all_regions} 张")

    if total_all_regions == 0:
        print("\n⚠️  所有区域都没有找到网卡，请检查:")
        print("   1. AK/SK 是否正确")
        print("   2. 账号权限是否足够")
        print("   3. 是否确实存在网络接口")
        exit()

    # 使用所有区域的数据进行后续处理
    configuration = volcenginesdkcore.Configuration()
    configuration.ak = ak
    configuration.sk = sk
    configuration.region = "cn-shanghai"  # 设置一个默认区域用于显示
    volcenginesdkcore.Configuration.set_default(configuration)

    # 创建模拟response对象
    class MockResponse:
        def __init__(self, interfaces):
            self.network_interface_sets = interfaces

    response = MockResponse(all_regions_interfaces)

    try:
        print(f"\n✅ 开始分析网卡数据...")

        # 1. 统计网卡总数
        total_interfaces = len(response.network_interface_sets)
        print(f"\n📊 最终网卡总数统计: {total_interfaces} 张网卡")
        print(f"� 数据来源: 多区域汇总")

        # 统计网卡状态分布
        status_count = {}
        type_count = {}
        region_count = {}

        for interface in response.network_interface_sets:
            status = safe_get_attr(interface, 'status', 'Unknown')
            interface_type = safe_get_attr(interface, 'type', 'Unknown')
            zone_id = safe_get_attr(interface, 'zone_id', 'Unknown')

            status_count[status] = status_count.get(status, 0) + 1
            type_count[interface_type] = type_count.get(interface_type, 0) + 1

            # 从zone_id推断区域
            if zone_id and zone_id != 'Unknown':
                region = zone_id.rsplit('-', 1)[0] if '-' in zone_id else zone_id
                region_count[region] = region_count.get(region, 0) + 1

        print(f"\n📈 网卡状态分布:")
        for status, count in status_count.items():
            print(f"   {status}: {count} 张")

        print(f"\n🔧 网卡类型分布:")
        for itype, count in type_count.items():
            print(f"   {itype}: {count} 张")

        if region_count:
            print(f"\n🌍 网卡区域分布:")
            for region, count in region_count.items():
                print(f"   {region}: {count} 张")

        # 2. 输出网卡ID、绑定对象名称、绑定对象tag
        if response.network_interface_sets:
            print("\n" + "=" * 80)
            print("📋 网卡绑定信息汇总")
            print("=" * 80)
            print(f"{'网卡ID':<25} {'绑定对象名称':<20} {'绑定对象Tag'}")
            print("-" * 80)

            for interface in response.network_interface_sets:
                interface_id = safe_get_attr(interface, 'network_interface_id', 'N/A')
                interface_name = safe_get_attr(interface, 'network_interface_name', 'N/A')

                # 获取标签信息
                tags = safe_get_attr(interface, 'tags')
                tag_str = ""

                if tags:
                    tags_list = safe_iterate(tags)
                    tag_pairs = []
                    for tag in tags_list:
                        key = safe_get_attr(tag, 'key', 'N/A')
                        value = safe_get_attr(tag, 'value', 'N/A')
                        tag_pairs.append(f"{key}={value}")
                    tag_str = "; ".join(tag_pairs) if tag_pairs else ""

                # 输出格式化信息
                print(f"{interface_id:<25} {interface_name:<20} {tag_str}")

            print("-" * 80)
            print(f"总计: {total_interfaces} 张网卡")

            # 额外提供JSON格式的结构化数据
            print("\n" + "=" * 80)
            print("� 结构化数据 (JSON格式)")
            print("=" * 80)

            structured_data = {
                "total_count": total_interfaces,
                "interfaces": []
            }

            for interface in response.network_interface_sets:
                interface_id = safe_get_attr(interface, 'network_interface_id', 'N/A')
                interface_name = safe_get_attr(interface, 'network_interface_name', 'N/A')

                # 获取标签信息
                tags = safe_get_attr(interface, 'tags')
                tag_dict = {}

                if tags:
                    tags_list = safe_iterate(tags)
                    for tag in tags_list:
                        key = safe_get_attr(tag, 'key', 'N/A')
                        value = safe_get_attr(tag, 'value', 'N/A')
                        tag_dict[key] = value

                structured_data["interfaces"].append({
                    "network_interface_id": interface_id,
                    "binding_object_name": interface_name,
                    "tags": tag_dict if tag_dict else {}
                })

            print(json.dumps(structured_data, indent=2, ensure_ascii=False))

        else:
            print("⚠️  未找到任何网络接口")
            print("💡 建议检查:")
            print("   1. 当前区域是否正确")
            print("   2. 账号权限是否足够")
            print("   3. 是否有网卡存在于其他区域")

    except ApiException as e:
        print(f"❌ API调用失败:")
        print(f"错误代码: {e.status}")
        print(f"错误信息: {e.reason}")
        print(f"错误详情: {e.body}")
    except Exception as e:
        print(f"❌ 程序执行出错: {str(e)}")
        import traceback
        traceback.print_exc()
