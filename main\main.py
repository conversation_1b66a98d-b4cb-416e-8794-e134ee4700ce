# Example Code generated by Beijing Volcanoengine Technology.
from __future__ import print_function
import volcenginesdkcore
import volcenginesdkvpc
from volcenginesdkcore.rest import ApiException
import json

def safe_get_attr(obj, attr_name, default=None):
    """安全获取对象属性"""
    try:
        return getattr(obj, attr_name, default)
    except Exception:
        return default

def safe_iterate(obj):
    """安全迭代对象，如果不可迭代则返回单元素列表"""
    if obj is None:
        return []

    # 如果是字符串，不要迭代
    if isinstance(obj, str):
        return [obj]

    # 尝试迭代
    try:
        # 检查是否有 __iter__ 方法
        if hasattr(obj, '__iter__'):
            return list(obj)
        else:
            return [obj]
    except Exception:
        return [obj]

# 移除了详细打印函数，保持代码简洁

# 移除了复杂的分析函数，保持代码简洁专注

if __name__ == '__main__':
    # 注意示例代码安全，代码泄漏会导致AK/SK泄漏，有极大的安全风险。
    configuration = volcenginesdkcore.Configuration()
    configuration.ak = "AKLTMGVmYWFiOTRlYzJhNDJkNmFkNmY2YmVmZjc5MTNiNjI"
    configuration.sk = "T0RaaE9UY3hZVEkwTnpNM05EQXpPV0k0TW1OaE9UWm1PV0ZoTWpkak9Eaw=="
    configuration.region = "cn-shanghai"
    # set default configuration
    volcenginesdkcore.Configuration.set_default(configuration)

    # use global default configuration
    api_instance = volcenginesdkvpc.VPCApi()
    describe_network_interfaces_request = volcenginesdkvpc.DescribeNetworkInterfacesRequest()

    try:
        print("🚀 开始查询网络接口信息...")

        # 调用API并获取返回值
        response = api_instance.describe_network_interfaces(describe_network_interfaces_request)

        print(f"✅ API调用成功!")

        # 1. 统计网卡总数
        total_interfaces = len(response.network_interface_sets)
        print(f"\n📊 网卡总数统计: {total_interfaces} 张网卡")

        # 2. 输出网卡ID、绑定对象名称、绑定对象tag
        if response.network_interface_sets:
            print("\n" + "=" * 80)
            print("📋 网卡绑定信息汇总")
            print("=" * 80)
            print(f"{'网卡ID':<25} {'绑定对象名称':<20} {'绑定对象Tag'}")
            print("-" * 80)

            for interface in response.network_interface_sets:
                interface_id = safe_get_attr(interface, 'network_interface_id', 'N/A')
                interface_name = safe_get_attr(interface, 'network_interface_name', 'N/A')

                # 获取标签信息
                tags = safe_get_attr(interface, 'tags')
                tag_str = ""

                if tags:
                    tags_list = safe_iterate(tags)
                    tag_pairs = []
                    for tag in tags_list:
                        key = safe_get_attr(tag, 'key', 'N/A')
                        value = safe_get_attr(tag, 'value', 'N/A')
                        tag_pairs.append(f"{key}={value}")
                    tag_str = "; ".join(tag_pairs) if tag_pairs else ""

                # 输出格式化信息
                print(f"{interface_id:<25} {interface_name:<20} {tag_str}")

            print("-" * 80)
            print(f"总计: {total_interfaces} 张网卡")

            # 额外提供JSON格式的结构化数据
            print("\n" + "=" * 80)
            print("� 结构化数据 (JSON格式)")
            print("=" * 80)

            structured_data = {
                "total_count": total_interfaces,
                "interfaces": []
            }

            for interface in response.network_interface_sets:
                interface_id = safe_get_attr(interface, 'network_interface_id', 'N/A')
                interface_name = safe_get_attr(interface, 'network_interface_name', 'N/A')

                # 获取标签信息
                tags = safe_get_attr(interface, 'tags')
                tag_dict = {}

                if tags:
                    tags_list = safe_iterate(tags)
                    for tag in tags_list:
                        key = safe_get_attr(tag, 'key', 'N/A')
                        value = safe_get_attr(tag, 'value', 'N/A')
                        tag_dict[key] = value

                structured_data["interfaces"].append({
                    "network_interface_id": interface_id,
                    "binding_object_name": interface_name,
                    "tags": tag_dict if tag_dict else {}
                })

            print(json.dumps(structured_data, indent=2, ensure_ascii=False))

        else:
            print("⚠️  未找到任何网络接口")

    except ApiException as e:
        print(f"❌ API调用失败:")
        print(f"错误代码: {e.status}")
        print(f"错误信息: {e.reason}")
        print(f"错误详情: {e.body}")
    except Exception as e:
        print(f"❌ 程序执行出错: {str(e)}")
        import traceback
        traceback.print_exc()
