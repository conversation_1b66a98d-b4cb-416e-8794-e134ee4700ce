# Example Code generated by Beijing Volcanoengine Technology.
from __future__ import print_function
import volcenginesdkcore
import volcenginesdkvpc
from volcenginesdkcore.rest import ApiException

if __name__ == '__main__':
    # 注意示例代码安全，代码泄漏会导致AK/SK泄漏，有极大的安全风险。
    configuration = volcenginesdkcore.Configuration()
    configuration.ak = "Your AK"
    configuration.sk = "Your SK"
    configuration.region = "cn-shanghai"
    # set default configuration
    volcenginesdkcore.Configuration.set_default(configuration)

    # use global default configuration
    api_instance = volcenginesdkvpc.VPCApi()
    describe_network_interfaces_request = volcenginesdkvpc.DescribeNetworkInterfacesRequest(
    )
    
    try:
        # 复制代码运行示例，请自行打印API返回值。
        api_instance.describe_network_interfaces(describe_network_interfaces_request)
    except ApiException as e:
        # 复制代码运行示例，请自行打印API错误信息。
        # print("Exception when calling api: %s\n" % e)
        pass
