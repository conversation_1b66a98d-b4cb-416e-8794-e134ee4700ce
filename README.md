# Python Daemon 示例

这是一个完整的 Python daemon 实现，包含基础版本和增强版本。

## 文件说明

- `daemon.py` - 基础 daemon 类和示例
- `enhanced_daemon.py` - 增强版 daemon，包含系统监控功能
- `daemon_config.py` - 配置文件管理
- `daemon.conf` - 配置文件（自动生成）

## 功能特性

### 基础版本 (daemon.py)
- 标准的 Unix daemon 实现
- 支持 start/stop/restart/status 操作
- PID 文件管理
- 日志记录
- 信号处理

### 增强版本 (enhanced_daemon.py)
- 包含基础版本的所有功能
- 系统监控（CPU、内存、磁盘使用率）
- 邮件告警功能
- 配置文件支持
- 阈值告警
- 防止重复告警

## 安装依赖

```bash
pip install psutil
```

## 使用方法

### 基础版本

```bash
# 启动 daemon
python daemon.py start

# 停止 daemon
python daemon.py stop

# 重启 daemon
python daemon.py restart

# 查看状态
python daemon.py status

# 自定义 PID 和日志文件
python daemon.py start --pidfile /var/run/mydaemon.pid --logfile /var/log/mydaemon.log
```

### 增强版本

```bash
# 启动增强版 daemon
python enhanced_daemon.py start

# 使用自定义配置文件
python enhanced_daemon.py start --config /etc/mydaemon.conf

# 重新加载配置（发送 SIGUSR1 信号）
kill -USR1 $(cat /tmp/enhanced_daemon.pid)
```

## 配置文件

增强版 daemon 支持配置文件，首次运行时会自动创建 `daemon.conf`：

```ini
[DEFAULT]
pidfile = /tmp/mydaemon.pid
logfile = /tmp/mydaemon.log
workdir = /
log_level = INFO
check_interval = 10

[monitoring]
disk_threshold = 90
memory_threshold = 85
cpu_threshold = 80
enable_email_alerts = false
email_smtp_server = smtp.example.com
email_smtp_port = 587
email_username = 
email_password = 
email_to = <EMAIL>
```

## 配置说明

- `check_interval`: 监控检查间隔（秒）
- `disk_threshold`: 磁盘使用率告警阈值（百分比）
- `memory_threshold`: 内存使用率告警阈值（百分比）
- `cpu_threshold`: CPU 使用率告警阈值（百分比）
- `enable_email_alerts`: 是否启用邮件告警
- 邮件相关配置：SMTP 服务器设置

## 日志示例

```
2024-01-15 10:30:01,123 - daemon - INFO - Daemon 启动成功
2024-01-15 10:30:01,124 - daemon - INFO - 执行监控任务 #1
2024-01-15 10:30:01,125 - daemon - INFO - 磁盘 /: 45.2% 已使用
2024-01-15 10:30:01,126 - daemon - INFO - 内存使用率: 67.8%
2024-01-15 10:30:01,127 - daemon - INFO - CPU 使用率: 23.4%
2024-01-15 10:30:01,128 - daemon - INFO - 系统运行时间: 5 days, 12:34:56
```

## 告警功能

当系统资源使用率超过配置的阈值时，daemon 会：
1. 在日志中记录警告信息
2. 发送邮件告警（如果启用）
3. 防止重复告警（每小时最多一次）

## 信号处理

- `SIGTERM/SIGINT`: 优雅退出
- `SIGUSR1`: 重新加载配置文件（仅增强版）

## 自定义业务逻辑

要添加自己的业务逻辑，可以：

1. 继承 `Daemon` 或 `EnhancedDaemon` 类
2. 重写 `run()` 方法
3. 添加自己的监控和处理逻辑

示例：

```python
class MyCustomDaemon(EnhancedDaemon):
    def run(self):
        self.logger.info("自定义 Daemon 启动")
        
        while self.running:
            # 您的业务逻辑
            self.do_custom_work()
            
            # 调用父类的系统监控
            self.check_system_status()
            
            time.sleep(self.config.getint('DEFAULT', 'check_interval'))
    
    def do_custom_work(self):
        # 实现您的具体业务逻辑
        pass
```

## 注意事项

1. 确保有足够的权限创建 PID 文件和日志文件
2. 在生产环境中，建议使用 systemd 或其他进程管理器
3. 定期检查日志文件大小，避免占用过多磁盘空间
4. 邮件告警功能需要正确配置 SMTP 服务器信息

## 系统服务集成

可以创建 systemd 服务文件：

```ini
[Unit]
Description=My Python Daemon
After=network.target

[Service]
Type=forking
User=daemon
Group=daemon
ExecStart=/usr/bin/python3 /path/to/enhanced_daemon.py start
ExecStop=/usr/bin/python3 /path/to/enhanced_daemon.py stop
ExecReload=/bin/kill -USR1 $MAINPID
PIDFile=/tmp/enhanced_daemon.pid
Restart=always

[Install]
WantedBy=multi-user.target
```

然后使用 systemctl 管理：

```bash
sudo systemctl enable mydaemon
sudo systemctl start mydaemon
sudo systemctl status mydaemon
```
